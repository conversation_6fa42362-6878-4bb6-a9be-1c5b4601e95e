import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/response/profile_response.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/account/account.dart';
import 'package:page/src/features/views/projects/widgets/sections/available_units_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/basic_information_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/descriptions_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/floor_plans_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/gallery_images_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/location_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/price_plan_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/register_interest_section.dart';
import 'package:page/src/features/views/projects/widgets/sections/rera_permit_section.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vertical_scrollable_tabview/vertical_scrollable_tabview.dart';

import '../../bloc/profile_bloc.dart';
import '../../controllers/currency_controller.dart';
import '../property_details/widgets/property_details.dart';

class ProjectDetailsPage extends StatefulWidget {
  final VideoModel project;

  const ProjectDetailsPage({
    super.key,
    required this.project,
  });

  @override
  State<ProjectDetailsPage> createState() => _ProjectDetailsPageState();
}

class _ProjectDetailsPageState extends State<ProjectDetailsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AutoScrollController _autoScrollController;

  late List<Widget> _sectionData;
  final CurrencyController _currencyController = CurrencyController();

  // Profile data state
  ProfileResponse? _profileData;
  bool _isProfileDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _currencyController.getcuurentcurrency(context);
    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _autoScrollController = AutoScrollController();

    // Load profile data once
    _loadProfileData();

    _initializeSectionData();
  }

  Future<void> _loadProfileData() async {
    bloc2.getProfile();
    // Listen to the stream to get the profile data
    bloc2.subject.stream.listen((data) {
      if (data != null && !_isProfileDataLoaded) {
        setState(() {
          _profileData = data;
          _isProfileDataLoaded = true;
          // Reinitialize section data with profile data
          _initializeSectionData();
        });
      }
    });
  }

  final List<String> _tabTitles = [
    // 'Gallery',
    // 'Basic Info',
    // 'Available Units',
    // 'Description',
    // 'Payment Plan',
    // 'Floor Plans',
    'Location',
    'Register Your Interest',
    'RERA Permit',
  ];

  void _initializeSectionData() {
    _sectionData = [
      // GalleryImagesSection(project: widget.project),
      // BasicInformationSection(project: widget.project),
      // AvailableUnitsSection(project: widget.project),
      // DescriptionsSection(project: widget.project),
      // PricePlanSection(project: widget.project),
      // FloorPlansSection(project: widget.project),
      LocationSection(project: widget.project),
      RegisterInterestSection(
        project: widget.project,
        profileData: _profileData,
        isProfileDataLoaded: _isProfileDataLoaded,
      ),
      ReraPermitSection(project: widget.project),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    _autoScrollController.dispose();
    super.dispose();
  }

  Future<void> _makePhoneCall() async {
    try {
      final phone = appConfiguration?.results['phone'] ?? '+97142454824';
      final String phoneNumber = phone.replaceAll(RegExp(r'[^0-9]'), '');
      log('Call Phone $phoneNumber');
      await callNumber(phoneNumber);
    } catch (e) {
      log('Cannot open $e');
    }
  }

  Future<void> _openWhatsApp() async {
    try {
      final String whatsapp =
          '${appConfiguration?.results['whatsapp'] ?? '+97142454824'}';
      final body = 'Project Name: ${widget.project.name}\n'
          'I am interested in this project.';
      var whatsappUrl = Uri.parse("https://wa.me/$whatsapp?text=$body");
      await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
    } catch (e) {
      log('Cannot open $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      floatingActionButton: Stack(
        children: [
          // WhatsApp button (right)
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              onPressed: _openWhatsApp,
              backgroundColor: const Color(0xFF25D366),
              heroTag: "whatsapp",
              child: const Icon(
                FontAwesomeIcons.whatsapp,
                color: Colors.white,
              ),
            ),
          ),
          // Phone call button (left)
          Positioned(
            bottom: 16,
            left: 32,
            child: FloatingActionButton(
              onPressed: _makePhoneCall,
              backgroundColor: primaryColor,
              heroTag: "phone",
              child: const Icon(
                Icons.phone,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      body: VerticalScrollableTabView(
        autoScrollController: _autoScrollController,
        tabController: _tabController,
        listItemData: _sectionData,
        verticalScrollPosition: VerticalScrollPosition.begin,
        eachItemChild: (object, index) => Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_tabTitles[index] != 'Gallery' &&
                    _tabTitles[index] != 'RERA Permit' &&
                    _tabTitles[index] != 'Location') ...[
                  Text(
                    AppLocalizations.of(context).translate(_tabTitles[index]),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF27b4a8),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                object as Widget,
              ],
            ),
          ),
        ),
        slivers: [
          SliverAppBar(
            // expandedHeight: 300,
            surfaceTintColor: Colors.transparent,
            pinned: true,
            backgroundColor: primaryColor,
            centerTitle: true,
            title: Text(
              widget.project.name ??
                  AppLocalizations.of(context).translate('Project Details'),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
              ),
            ),
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabAlignment: TabAlignment.start,
              labelStyle: TextStyle(
                fontWeight: FontWeight.w600,
                fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
                fontSize: 14,
              ),
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
                fontSize: 14,
              ),
              onTap: (index) {
                VerticalScrollableTabBarStatus.setIndex(index);
              },
              tabs: _tabTitles
                  .map((title) => Tab(
                        text: AppLocalizations.of(context).translate(title),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}
