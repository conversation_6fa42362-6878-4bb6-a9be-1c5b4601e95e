import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/map/show_on_map/projects_map_page.dart';
import 'package:webview_flutter/webview_flutter.dart';

class LocationSection extends StatefulWidget {
  final VideoModel project;

  const LocationSection({
    super.key,
    required this.project,
  });

  @override
  State<LocationSection> createState() => _LocationSectionState();
}

class _LocationSectionState extends State<LocationSection> {
  @override
  Widget build(BuildContext context) {
    if (widget.project.latitude == null || widget.project.longitude == null) {
      return _buildNoLocationWidget(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location info
        if (widget.project.locationName?.isNotEmpty == true) ...[
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: Colors.green[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).translate('Location'),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        widget.project.locationName!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Map preview - Using WebView to prevent rebuilds
        _buildWebViewMap(context),

        // const SizedBox(height: 16),
        //
        // // Action buttons
        // Row(
        //   children: [
        //     Expanded(
        //       child: ElevatedButton.icon(
        //         onPressed: () => _openFullMap(context),
        //         icon: const Icon(Icons.map, size: 18),
        //         label: Text(
        //           AppLocalizations.of(context).translate('View on Map'),
        //           style: const TextStyle(fontSize: 14),
        //         ),
        //         style: ElevatedButton.styleFrom(
        //           backgroundColor: const Color(0xFF27b4a8),
        //           foregroundColor: Colors.white,
        //           padding: const EdgeInsets.symmetric(vertical: 12),
        //           shape: RoundedRectangleBorder(
        //             borderRadius: BorderRadius.circular(8),
        //           ),
        //         ),
        //       ),
        //     ),
        //     const SizedBox(width: 12),
        //     Expanded(
        //       child: OutlinedButton.icon(
        //         onPressed: () => _openDirections(context),
        //         icon: const Icon(Icons.directions, size: 18),
        //         label: Text(
        //           AppLocalizations.of(context).translate('Get Directions'),
        //           style: const TextStyle(fontSize: 14),
        //         ),
        //         style: OutlinedButton.styleFrom(
        //           foregroundColor: const Color(0xFF27b4a8),
        //           side: const BorderSide(color: Color(0xFF27b4a8)),
        //           padding: const EdgeInsets.symmetric(vertical: 12),
        //           shape: RoundedRectangleBorder(
        //             borderRadius: BorderRadius.circular(8),
        //           ),
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
      ],
    );
  }

  Widget _buildWebViewMap(BuildContext context) {
    final lat = widget.project.latitude!;
    final lng = widget.project.longitude!;

    // Simple Google Maps URL without API key
    final simpleMapUrl =
        'https://maps.google.com/maps?q=$lat,$lng&z=15&output=embed';

    return Container(
      height: 340,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // WebView with Google Maps
            WebViewWidget(
              controller: WebViewController()
                ..setJavaScriptMode(JavaScriptMode.unrestricted)
                ..loadRequest(Uri.parse(simpleMapUrl)),
            ),
            // Tap overlay to open full map
            Positioned.fill(
              child: GestureDetector(
                onTap: () => _openFullMap(context),
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
            // Map type toggle button (optional)
            // Positioned(
            //   top: 10,
            //   right: 10,
            //   child: Container(
            //     decoration: BoxDecoration(
            //       color: Colors.white,
            //       borderRadius: BorderRadius.circular(8),
            //       boxShadow: [
            //         BoxShadow(
            //           color: Colors.black.withOpacity(0.2),
            //           blurRadius: 4,
            //           offset: const Offset(0, 2),
            //         ),
            //       ],
            //     ),
            //     child: Material(
            //       color: Colors.transparent,
            //       child: InkWell(
            //         borderRadius: BorderRadius.circular(8),
            //         onTap: () => _openFullMap(context),
            //         child: const Padding(
            //           padding: EdgeInsets.all(8),
            //           child: Row(
            //             mainAxisSize: MainAxisSize.min,
            //             children: [
            //               Icon(
            //                 Icons.fullscreen,
            //                 size: 16,
            //                 color: Colors.grey,
            //               ),
            //               SizedBox(width: 4),
            //               Text(
            //                 'Full Map',
            //                 style: TextStyle(
            //                   fontSize: 12,
            //                   color: Colors.grey,
            //                   fontWeight: FontWeight.w500,
            //                 ),
            //               ),
            //             ],
            //           ),
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoLocationWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.location_off,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context).translate('Location Not Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)
                  .translate('Location information will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullMap(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProjectsMapPage(
          initialLat: widget.project.latitude,
          initialLng: widget.project.longitude,
        ),
      ),
    );
  }
}
